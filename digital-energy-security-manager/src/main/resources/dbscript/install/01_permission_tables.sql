-- 权限管理系统数据库表结构
-- 支持幂等性执行

-- 1. 菜单表
CREATE TABLE IF NOT EXISTS t_menu (
    menu_id VARCHAR(100) PRIMARY KEY,
    menu_name VARCHAR(100) NOT NULL COMMENT '菜单名称',
    parent_id BIGINT COMMENT '父菜单ID',
    menu_path VARCHAR(200) COMMENT '菜单路径',
    sort_order INTEGER COMMENT '排序号',
    menu_level INTEGER COMMENT '菜单层级',
    create_time TIMESTAMPZ COMMENT '创建时间',
    update_time TIMESTAMPZ COMMENT '更新时间'
);

-- 创建菜单表索引
CREATE INDEX IF NOT EXISTS idx_menu_parent_id ON t_menu(parent_id);

-- 2. 角色表
CREATE TABLE IF NOT EXISTS t_role (
    role_id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(20) NOT NULL COMMENT '角色名称',
    role_description VARCHAR(200) COMMENT '角色描述',
    role_type VARCHAR(20) DEFAULT 'CUSTOM' COMMENT '角色类型：DEFAULT-默认角色，CUSTOM-自定义角色',
    create_time TIMESTAMPZ COMMENT '创建时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_time TIMESTAMPZ COMMENT '更新时间',
    update_by VARCHAR(50) COMMENT '更新人'
);

-- 创建角色表索引
CREATE INDEX IF NOT EXISTS idx_role_type ON t_role(role_type);

-- 3. 用户表
CREATE TABLE IF NOT EXISTS t_user (
    user_id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    user_code VARCHAR(50) NOT NULL UNIQUE COMMENT '用户工号',
    organization VARCHAR(200) COMMENT '组织机构',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by VARCHAR(50) COMMENT '更新人'
);

-- 创建用户表索引
CREATE INDEX IF NOT EXISTS idx_user_code ON t_user(user_code);

-- 4. 用户组表
CREATE TABLE IF NOT EXISTS t_user_group (
    user_group_id BIGSERIAL PRIMARY KEY,
    group_name VARCHAR(50) NOT NULL COMMENT '用户组名称',
    group_description TEXT COMMENT '用户组描述',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR(50) COMMENT '创建人',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by VARCHAR(50) COMMENT '更新人'
);

-- 创建用户组表索引（暂无需要索引的字段）

-- 5. 角色菜单关联表
CREATE TABLE IF NOT EXISTS t_role_menu (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL COMMENT '角色ID',
    menu_id BIGINT NOT NULL COMMENT '菜单ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
);

-- 创建角色菜单关联表索引
CREATE INDEX IF NOT EXISTS idx_role_menu_role_id ON t_role_menu(role_id);
CREATE INDEX IF NOT EXISTS idx_role_menu_menu_id ON t_role_menu(menu_id);

-- 6. 用户角色关联表
CREATE TABLE IF NOT EXISTS t_user_role (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
);

-- 创建用户角色关联表索引
CREATE INDEX IF NOT EXISTS idx_user_role_user_id ON t_user_role(user_id);
CREATE INDEX IF NOT EXISTS idx_user_role_role_id ON t_user_role(role_id);

-- 7. 用户组角色关联表
CREATE TABLE IF NOT EXISTS t_user_group_role (
    id BIGSERIAL PRIMARY KEY,
    user_group_id BIGINT NOT NULL COMMENT '用户组ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
);

-- 创建用户组角色关联表索引
CREATE INDEX IF NOT EXISTS idx_user_group_role_group_id ON t_user_group_role(user_group_id);
CREATE INDEX IF NOT EXISTS idx_user_group_role_role_id ON t_user_group_role(role_id);

-- 8. 用户组成员关联表
CREATE TABLE IF NOT EXISTS t_user_group_member (
    id BIGSERIAL PRIMARY KEY,
    user_group_id BIGINT NOT NULL COMMENT '用户组ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
);

-- 创建用户组成员关联表索引
CREATE INDEX IF NOT EXISTS idx_user_group_member_group_id ON t_user_group_member(user_group_id);
CREATE INDEX IF NOT EXISTS idx_user_group_member_user_id ON t_user_group_member(user_id);


